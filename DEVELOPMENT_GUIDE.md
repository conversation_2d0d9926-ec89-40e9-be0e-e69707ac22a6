# Sai Harvest Foods - Development Guide

## Quick Start

### Prerequisites
- Android Studio Hedgehog (2023.1.1) or later
- JDK 8 or higher
- Android SDK API 24+ (Android 7.0)
- Git for version control

### Setup Steps
1. **Clone/Open Project**
   ```bash
   # If cloning from repository
   git clone <repository-url>
   cd SaiHarvestFoods
   
   # Open in Android Studio
   # File -> Open -> Select project folder
   ```

2. **Sync Dependencies**
   - Android Studio will prompt to sync Gradle files
   - Click "Sync Now" and wait for completion
   - All dependencies will be downloaded automatically

3. **Run the App**
   - Connect Android device or start emulator (API 24+)
   - Click "Run" button or press Shift+F10
   - App will build and install automatically

## Testing the App

### Authentication Flow
1. **Registration**:
   - Open app (starts on Login screen)
   - Click "Sign Up" to go to Registration
   - Fill in all fields with valid data
   - Test form validation by entering invalid data
   - Complete registration

2. **Login**:
   - Use registered credentials to login
   - Test "Remember me" functionality
   - Test form validation

3. **Navigation**:
   - Navigate between Products, Orders, Profile screens
   - Test back navigation
   - Test logout functionality

### Theme Testing
- Switch device between light/dark mode
- Verify app theme changes automatically
- Check all screens in both themes

## Development Workflow

### Adding New Features
1. **Domain First**: Start with domain models and use cases
2. **Data Layer**: Implement repository and data sources
3. **Presentation**: Create ViewModels and Compose screens
4. **Navigation**: Add new routes and navigation logic
5. **Testing**: Write unit tests for business logic

### Code Style
- Follow Kotlin coding conventions
- Use meaningful variable and function names
- Add KDoc comments for public APIs
- Keep functions small and focused
- Use dependency injection (Hilt)

### Architecture Guidelines
```
presentation/ (UI, ViewModels, Navigation)
    ↓
domain/ (Use Cases, Models, Repository Interfaces)
    ↓
data/ (Repository Implementations, Data Sources)
```

## Phase 2: Product Catalog (Next Steps)

### Planned Features
1. **Product Models**
   - Product entity with Room database
   - Category management
   - Price and inventory tracking

2. **Product Screens**
   - Product list with categories
   - Search and filter functionality
   - Product detail screen
   - Shopping cart

3. **API Integration**
   - Retrofit service for product data
   - Image loading with Coil
   - Offline caching strategy

### Implementation Order
1. Create Product domain models
2. Set up product database entities
3. Implement product repository
4. Create product use cases
5. Build product list UI
6. Add search functionality
7. Implement shopping cart

## Troubleshooting

### Common Issues
1. **Build Errors**
   - Clean and rebuild: Build -> Clean Project -> Rebuild
   - Invalidate caches: File -> Invalidate Caches and Restart

2. **Dependency Issues**
   - Check internet connection
   - Sync Gradle files again
   - Update Android Studio if needed

3. **Emulator Issues**
   - Use API 24+ emulator
   - Ensure sufficient RAM (4GB+)
   - Enable hardware acceleration

### Getting Help
- Check Android Studio's Build Output for specific errors
- Review Logcat for runtime issues
- Consult official Android documentation
- Use Android Studio's built-in help system

## Code Quality

### Before Committing
- [ ] Code compiles without warnings
- [ ] All new features have unit tests
- [ ] UI tested on different screen sizes
- [ ] Dark/light theme compatibility verified
- [ ] No hardcoded strings (use string resources)
- [ ] Proper error handling implemented

### Testing Commands
```bash
# Run unit tests
./gradlew test

# Run validation script
./validate_project.sh

# Check project structure
./verify_build.sh
```

## Security Checklist

### Data Protection
- [ ] No sensitive data in logs
- [ ] Encrypted storage for user data
- [ ] HTTPS for all network calls
- [ ] Input validation on all forms
- [ ] Proper session management

### Best Practices
- Use Android Keystore for encryption keys
- Implement certificate pinning for production
- Regular security audits
- Follow OWASP Mobile Security guidelines

## Performance Tips

### Optimization
- Use LazyColumn for large lists
- Implement proper image caching
- Minimize recomposition in Compose
- Use background threads for heavy operations
- Implement proper loading states

### Memory Management
- Avoid memory leaks in ViewModels
- Use lifecycle-aware components
- Proper coroutine scope management
- Optimize image loading and caching

---

**Happy Coding!** 🚀

For questions or issues, refer to the project documentation or create an issue in the repository.

<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="md_theme_dark_background">#0F1414</color>
    <color name="md_theme_dark_error">#FFB4AB</color>
    <color name="md_theme_dark_errorContainer">#93000A</color>
    <color name="md_theme_dark_inverseOnSurface">#2D3131</color>
    <color name="md_theme_dark_inversePrimary">#006A6B</color>
    <color name="md_theme_dark_inverseSurface">#DFE3E2</color>
    <color name="md_theme_dark_onBackground">#DFE3E2</color>
    <color name="md_theme_dark_onError">#690005</color>
    <color name="md_theme_dark_onErrorContainer">#FFDAD6</color>
    <color name="md_theme_dark_onPrimary">#003738</color>
    <color name="md_theme_dark_onPrimaryContainer">#6FF7F8</color>
    <color name="md_theme_dark_onSecondary">#1C3535</color>
    <color name="md_theme_dark_onSecondaryContainer">#CCE8E7</color>
    <color name="md_theme_dark_onSurface">#DFE3E2</color>
    <color name="md_theme_dark_onSurfaceVariant">#BEC9C8</color>
    <color name="md_theme_dark_onTertiary">#0A344A</color>
    <color name="md_theme_dark_onTertiaryContainer">#CCE5FF</color>
    <color name="md_theme_dark_outline">#899392</color>
    <color name="md_theme_dark_outlineVariant">#3F4948</color>
    <color name="md_theme_dark_primary">#4DDADB</color>
    <color name="md_theme_dark_primaryContainer">#005051</color>
    <color name="md_theme_dark_secondary">#B1CCCB</color>
    <color name="md_theme_dark_secondaryContainer">#334B4B</color>
    <color name="md_theme_dark_surface">#0F1414</color>
    <color name="md_theme_dark_surfaceVariant">#3F4948</color>
    <color name="md_theme_dark_tertiary">#A4C9E8</color>
    <color name="md_theme_dark_tertiaryContainer">#284B61</color>
    <color name="md_theme_light_background">#FAFDFC</color>
    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_inverseOnSurface">#EFF1F0</color>
    <color name="md_theme_light_inversePrimary">#4DDADB</color>
    <color name="md_theme_light_inverseSurface">#2D3131</color>
    <color name="md_theme_light_onBackground">#191C1C</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_onPrimaryContainer">#002020</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_onSecondaryContainer">#051F1F</color>
    <color name="md_theme_light_onSurface">#191C1C</color>
    <color name="md_theme_light_onSurfaceVariant">#3F4948</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_onTertiaryContainer">#001E31</color>
    <color name="md_theme_light_outline">#6F7979</color>
    <color name="md_theme_light_outlineVariant">#BEC9C8</color>
    <color name="md_theme_light_primary">#006A6B</color>
    <color name="md_theme_light_primaryContainer">#6FF7F8</color>
    <color name="md_theme_light_secondary">#4A6363</color>
    <color name="md_theme_light_secondaryContainer">#CCE8E7</color>
    <color name="md_theme_light_surface">#FAFDFC</color>
    <color name="md_theme_light_surfaceVariant">#DAE5E4</color>
    <color name="md_theme_light_tertiary">#456179</color>
    <color name="md_theme_light_tertiaryContainer">#CCE5FF</color>
    <string name="already_have_account">Already have an account?</string>
    <string name="app_name">Sai Harvest Foods</string>
    <string name="cancel">Cancel</string>
    <string name="cart">Cart</string>
    <string name="confirm_password">Confirm Password</string>
    <string name="dont_have_account">Don\'t have an account?</string>
    <string name="email">Email</string>
    <string name="error">Error</string>
    <string name="error_empty_email">Email cannot be empty</string>
    <string name="error_empty_name">Name cannot be empty</string>
    <string name="error_empty_password">Password cannot be empty</string>
    <string name="error_empty_phone">Phone number cannot be empty</string>
    <string name="error_invalid_email">Please enter a valid email</string>
    <string name="error_invalid_phone">Please enter a valid phone number</string>
    <string name="error_password_too_short">Password must be at least 6 characters</string>
    <string name="error_passwords_dont_match">Passwords do not match</string>
    <string name="forgot_password">Forgot Password?</string>
    <string name="full_name">Full Name</string>
    <string name="loading">Loading...</string>
    <string name="login">Login</string>
    <string name="ok">OK</string>
    <string name="orders">Orders</string>
    <string name="password">Password</string>
    <string name="phone_number">Phone Number</string>
    <string name="products">Products</string>
    <string name="profile">Profile</string>
    <string name="register">Register</string>
    <string name="retry">Retry</string>
    <string name="sign_in">Sign In</string>
    <string name="sign_up">Sign Up</string>
    <style name="Theme.SaiHarvestOrderApp" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_light_outlineVariant</item>
        <item name="colorSurfaceInverse">@color/md_theme_light_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_light_inverseOnSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_light_inversePrimary</item>
    </style>
</resources>
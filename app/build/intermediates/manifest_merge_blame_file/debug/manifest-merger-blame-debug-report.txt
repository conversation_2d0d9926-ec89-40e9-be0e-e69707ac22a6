1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.saiharvest.orderapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Network permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:7:5-67
12-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:7:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:8:5-79
13-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:8:22-76
14
15    <permission
15-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
16        android:name="com.saiharvest.orderapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.saiharvest.orderapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
20
21    <application
21-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:10:5-33:19
22        android:name="com.saiharvest.orderapp.SaiHarvestApplication"
22-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:11:9-46
23        android:allowBackup="true"
23-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:12:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:13:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:14:9-54
29        android:icon="@mipmap/ic_launcher"
29-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:15:9-43
30        android:label="@string/app_name"
30-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:16:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:17:9-54
32        android:supportsRtl="true"
32-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:18:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.SaiHarvestOrderApp"
34-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:19:9-56
35        android:usesCleartextTraffic="false" >
35-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:20:9-45
36        <activity
36-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:23:9-32:20
37            android:name="com.saiharvest.orderapp.MainActivity"
37-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:24:13-41
38            android:exported="true"
38-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:25:13-36
39            android:label="@string/app_name"
39-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:26:13-45
40            android:theme="@style/Theme.SaiHarvestOrderApp" >
40-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:27:13-60
41            <intent-filter>
41-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:28:13-31:29
42                <action android:name="android.intent.action.MAIN" />
42-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:29:17-69
42-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:29:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:30:17-77
44-->/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:30:27-74
45            </intent-filter>
46        </activity>
47        <activity
47-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9c5dd19ac90c00c5bd54dc90759228a9/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
48            android:name="androidx.compose.ui.tooling.PreviewActivity"
48-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9c5dd19ac90c00c5bd54dc90759228a9/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
49            android:exported="true" />
49-->[androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9c5dd19ac90c00c5bd54dc90759228a9/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
50        <activity
50-->[androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/dd4d593c48c84c200722ed24578b24c2/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:23:9-25:39
51            android:name="androidx.activity.ComponentActivity"
51-->[androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/dd4d593c48c84c200722ed24578b24c2/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:24:13-63
52            android:exported="true" />
52-->[androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/dd4d593c48c84c200722ed24578b24c2/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:25:13-36
53
54        <provider
54-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:25:13-67
56            android:authorities="com.saiharvest.orderapp.androidx-startup"
56-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
59                android:name="androidx.emoji2.text.EmojiCompatInitializer"
59-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:30:17-75
60                android:value="androidx.startup" />
60-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
62                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
62-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
63                android:value="androidx.startup" />
63-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
65                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
65-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
66                android:value="androidx.startup" />
66-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
67        </provider>
68
69        <service
69-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
70            android:name="androidx.room.MultiInstanceInvalidationService"
70-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
71            android:directBootAware="true"
71-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
72            android:exported="false" />
72-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
73
74        <receiver
74-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
75            android:name="androidx.profileinstaller.ProfileInstallReceiver"
75-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
76            android:directBootAware="false"
76-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
77            android:enabled="true"
77-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
78            android:exported="true"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
79            android:permission="android.permission.DUMP" >
79-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
81                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
81-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
84                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
84-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
84-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
87                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
87-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
87-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
90                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
90-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
90-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
91            </intent-filter>
92        </receiver>
93    </application>
94
95</manifest>

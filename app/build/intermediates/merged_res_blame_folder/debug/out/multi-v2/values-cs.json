{"logs": [{"outputFile": "com.saiharvest.orderapp-mergeDebugResources-58:/values-cs/values-cs.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/ed7ef9edb6e585a5acd6a07df8623d40/transformed/material3-1.1.2/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,397,511,587,678,787,917,1029,1160,1241,1337,1425,1518,1630,1748,1849,1978,2104,2238,2397,2521,2634,2755,2873,2962,3055,3168,3279,3373,3472,3576,3703,3840,3946,4040,4120,4197,4280,4362,4456,4532,4614,4711,4810,4903,5000,5084,5186,5281,5379,5494,5570,5670", "endColumns": "113,112,114,113,75,90,108,129,111,130,80,95,87,92,111,117,100,128,125,133,158,123,112,120,117,88,92,112,110,93,98,103,126,136,105,93,79,76,82,81,93,75,81,96,98,92,96,83,101,94,97,114,75,99,90", "endOffsets": "164,277,392,506,582,673,782,912,1024,1155,1236,1332,1420,1513,1625,1743,1844,1973,2099,2233,2392,2516,2629,2750,2868,2957,3050,3163,3274,3368,3467,3571,3698,3835,3941,4035,4115,4192,4275,4357,4451,4527,4609,4706,4805,4898,4995,5079,5181,5276,5374,5489,5565,5665,5756"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,332,447,1468,1544,1635,1744,1874,1986,2117,2198,2294,2382,2475,2587,2705,2806,2935,3061,3195,3354,3478,3591,3712,3830,3919,4012,4125,4236,4330,4429,4533,4660,4797,4903,5193,5365,6090,6245,6428,6789,6865,6947,7044,7143,7236,7333,7417,7519,7614,7712,7827,7903,8003", "endColumns": "113,112,114,113,75,90,108,129,111,130,80,95,87,92,111,117,100,128,125,133,158,123,112,120,117,88,92,112,110,93,98,103,126,136,105,93,79,76,82,81,93,75,81,96,98,92,96,83,101,94,97,114,75,99,90", "endOffsets": "214,327,442,556,1539,1630,1739,1869,1981,2112,2193,2289,2377,2470,2582,2700,2801,2930,3056,3190,3349,3473,3586,3707,3825,3914,4007,4120,4231,4325,4424,4528,4655,4792,4898,4992,5268,5437,6168,6322,6517,6860,6942,7039,7138,7231,7328,7412,7514,7609,7707,7822,7898,7998,8089"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "561,659,761,862,961,1066,1173,6327", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "654,756,857,956,1061,1168,1287,6423"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/fae82ac325c7c1a20d3c61c3ee836a79/transformed/ui-release/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,375,477,569,647,739,830,911,980,1049,1131,1217,1289,1368,1436", "endColumns": "92,82,93,101,91,77,91,90,80,68,68,81,85,71,78,67,119", "endOffsets": "193,276,370,472,564,642,734,825,906,975,1044,1126,1212,1284,1363,1431,1551"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1292,1385,4997,5091,5273,5442,5520,5612,5703,5784,5853,5922,6004,6173,6522,6601,6669", "endColumns": "92,82,93,101,91,77,91,90,80,68,68,81,85,71,78,67,119", "endOffsets": "1380,1463,5086,5188,5360,5515,5607,5698,5779,5848,5917,5999,6085,6240,6596,6664,6784"}}]}]}
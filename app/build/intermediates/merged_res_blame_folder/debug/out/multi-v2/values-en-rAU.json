{"logs": [{"outputFile": "com.saiharvest.orderapp-mergeDebugResources-58:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/fae82ac325c7c1a20d3c61c3ee836a79/transformed/ui-release/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1337,1403,1524"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1267,1359,4941,5035,5208,5371,5453,5542,5631,5715,5780,5844,5922,6087,6442,6519,6585", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "1354,1436,5030,5129,5290,5448,5537,5626,5710,5775,5839,5917,5999,6155,6514,6580,6701"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/ed7ef9edb6e585a5acd6a07df8623d40/transformed/material3-1.1.2/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,498,576,667,776,908,1020,1152,1232,1327,1414,1507,1622,1743,1843,1966,2085,2209,2367,2484,2596,2716,2838,2926,3020,3133,3253,3346,3444,3542,3667,3802,3904,3998,4072,4148,4231,4314,4412,4488,4568,4665,4762,4858,4953,5037,5139,5236,5335,5451,5527,5623", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "164,276,383,493,571,662,771,903,1015,1147,1227,1322,1409,1502,1617,1738,1838,1961,2080,2204,2362,2479,2591,2711,2833,2921,3015,3128,3248,3341,3439,3537,3662,3797,3899,3993,4067,4143,4226,4309,4407,4483,4563,4660,4757,4853,4948,5032,5134,5231,5330,5446,5522,5618,5709"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,438,1441,1519,1610,1719,1851,1963,2095,2175,2270,2357,2450,2565,2686,2786,2909,3028,3152,3310,3427,3539,3659,3781,3869,3963,4076,4196,4289,4387,4485,4610,4745,4847,5134,5295,6004,6160,6344,6706,6782,6862,6959,7056,7152,7247,7331,7433,7530,7629,7745,7821,7917", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "214,326,433,543,1514,1605,1714,1846,1958,2090,2170,2265,2352,2445,2560,2681,2781,2904,3023,3147,3305,3422,3534,3654,3776,3864,3958,4071,4191,4284,4382,4480,4605,4740,4842,4936,5203,5366,6082,6238,6437,6777,6857,6954,7051,7147,7242,7326,7428,7525,7624,7740,7816,7912,8003"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "548,644,746,845,944,1048,1151,6243", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "639,741,840,939,1043,1146,1262,6339"}}]}]}
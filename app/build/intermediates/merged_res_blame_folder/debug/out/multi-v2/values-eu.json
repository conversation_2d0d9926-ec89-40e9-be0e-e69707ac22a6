{"logs": [{"outputFile": "com.saiharvest.orderapp-mergeDebugResources-58:/values-eu/values-eu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/ed7ef9edb6e585a5acd6a07df8623d40/transformed/material3-1.1.2/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,198,341,464,605,683,777,889,1025,1140,1281,1361,1462,1554,1650,1765,1881,1987,2126,2266,2397,2584,2705,2823,2944,3063,3156,3249,3373,3504,3598,3695,3797,3939,4086,4190,4285,4357,4434,4519,4603,4709,4785,4867,4958,5057,5144,5239,5325,5429,5525,5626,5740,5816,5916", "endColumns": "142,142,122,140,77,93,111,135,114,140,79,100,91,95,114,115,105,138,139,130,186,120,117,120,118,92,92,123,130,93,96,101,141,146,103,94,71,76,84,83,105,75,81,90,98,86,94,85,103,95,100,113,75,99,90", "endOffsets": "193,336,459,600,678,772,884,1020,1135,1276,1356,1457,1549,1645,1760,1876,1982,2121,2261,2392,2579,2700,2818,2939,3058,3151,3244,3368,3499,3593,3690,3792,3934,4081,4185,4280,4352,4429,4514,4598,4704,4780,4862,4953,5052,5139,5234,5320,5424,5520,5621,5735,5811,5911,6002"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,248,391,514,1558,1636,1730,1842,1978,2093,2234,2314,2415,2507,2603,2718,2834,2940,3079,3219,3350,3537,3658,3776,3897,4016,4109,4202,4326,4457,4551,4648,4750,4892,5039,5143,5443,5607,6345,6504,6689,7060,7136,7218,7309,7408,7495,7590,7676,7780,7876,7977,8091,8167,8267", "endColumns": "142,142,122,140,77,93,111,135,114,140,79,100,91,95,114,115,105,138,139,130,186,120,117,120,118,92,92,123,130,93,96,101,141,146,103,94,71,76,84,83,105,75,81,90,98,86,94,85,103,95,100,113,75,99,90", "endOffsets": "243,386,509,650,1631,1725,1837,1973,2088,2229,2309,2410,2502,2598,2713,2829,2935,3074,3214,3345,3532,3653,3771,3892,4011,4104,4197,4321,4452,4546,4643,4745,4887,5034,5138,5233,5510,5679,6425,6583,6790,7131,7213,7304,7403,7490,7585,7671,7775,7871,7972,8086,8162,8262,8353"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "655,753,856,956,1059,1164,1267,6588", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "748,851,951,1054,1159,1262,1381,6684"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/fae82ac325c7c1a20d3c61c3ee836a79/transformed/ui-release/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,985,1057,1145,1235,1309,1386,1454", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,980,1052,1140,1230,1304,1381,1449,1569"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1386,1477,5238,5339,5515,5684,5760,5847,5936,6020,6095,6167,6255,6430,6795,6872,6940", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "1472,1553,5334,5438,5602,5755,5842,5931,6015,6090,6162,6250,6340,6499,6867,6935,7055"}}]}]}
{"logs": [{"outputFile": "com.saiharvest.orderapp-mergeDebugResources-58:/values-pt/values-pt.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/ed7ef9edb6e585a5acd6a07df8623d40/transformed/material3-1.1.2/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,399,511,587,679,789,919,1033,1180,1260,1358,1449,1545,1656,1782,1885,2020,2154,2290,2452,2584,2700,2821,2945,3037,3130,3246,3358,3454,3561,3666,3802,3943,4049,4147,4229,4303,4388,4473,4570,4646,4726,4823,4925,5013,5108,5192,5300,5397,5496,5611,5687,5783", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "165,282,394,506,582,674,784,914,1028,1175,1255,1353,1444,1540,1651,1777,1880,2015,2149,2285,2447,2579,2695,2816,2940,3032,3125,3241,3353,3449,3556,3661,3797,3938,4044,4142,4224,4298,4383,4468,4565,4641,4721,4818,4920,5008,5103,5187,5295,5392,5491,5606,5682,5778,5866"}, "to": {"startLines": "2,3,4,5,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,49,51,60,62,64,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,337,449,1477,1553,1645,1755,1885,1999,2146,2226,2324,2415,2511,2622,2748,2851,2986,3120,3256,3418,3550,3666,3787,3911,4003,4096,4212,4324,4420,4527,4632,4768,4909,5015,5309,5477,6225,6386,6572,6925,7001,7081,7178,7280,7368,7463,7547,7655,7752,7851,7966,8042,8138", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "215,332,444,556,1548,1640,1750,1880,1994,2141,2221,2319,2410,2506,2617,2743,2846,2981,3115,3251,3413,3545,3661,3782,3906,3998,4091,4207,4319,4415,4522,4627,4763,4904,5010,5108,5386,5546,6305,6466,6664,6996,7076,7173,7275,7363,7458,7542,7650,7747,7846,7961,8037,8133,8221"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "6,7,8,9,10,11,12,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "561,658,760,859,959,1066,1176,6471", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "653,755,854,954,1061,1171,1291,6567"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/fae82ac325c7c1a20d3c61c3ee836a79/transformed/ui-release/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "13,14,47,48,50,52,53,54,55,56,57,58,59,61,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1296,1391,5113,5210,5391,5551,5634,5731,5822,5909,5981,6050,6135,6310,6669,6745,6812", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "1386,1472,5205,5304,5472,5629,5726,5817,5904,5976,6045,6130,6220,6381,6740,6807,6920"}}]}]}
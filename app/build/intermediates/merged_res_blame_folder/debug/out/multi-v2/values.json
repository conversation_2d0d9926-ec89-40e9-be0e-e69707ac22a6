{"logs": [{"outputFile": "com.saiharvest.orderapp-mergeDebugResources-58:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/f3c98bae8bedbff9bdfb42c4e1cc54f0/transformed/customview-poolingcontainer-1.0.0/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "126,130", "startColumns": "4,4", "startOffsets": "7919,8096", "endColumns": "53,66", "endOffsets": "7968,8158"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/ed7ef9edb6e585a5acd6a07df8623d40/transformed/material3-1.1.2/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "159,160,161,162,174,176,177,178,179,180,183,184,185,186,187,188,189,190,191,192,193,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,213,226,233,248,252,254,258,259,260,261,262,263,264,265,266,267,268,269,270,271", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9735,9819,9901,9978,10768,10878,10939,11018,11120,11202,11318,11368,11433,11490,11555,11640,11731,11801,11894,11983,12077,12222,12309,12393,12485,12579,12639,12703,12786,12876,12939,13007,13075,13172,13277,13349,13581,14443,14797,15508,15695,15819,16060,16106,16156,16223,16290,16356,16421,16475,16547,16614,16684,16766,16812,16878", "endLines": "159,160,161,162,174,176,177,178,179,182,183,184,185,186,187,188,189,190,191,192,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,213,226,233,248,252,254,258,259,260,261,262,263,264,265,266,267,268,269,270,271", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "9814,9896,9973,10053,10811,10934,11013,11115,11197,11313,11363,11428,11485,11550,11635,11726,11796,11889,11978,12072,12217,12304,12388,12480,12574,12634,12698,12781,12871,12934,13002,13070,13167,13272,13344,13409,13620,14484,14861,15556,15743,15882,16101,16151,16218,16285,16351,16416,16470,16542,16609,16679,16761,16807,16873,16934"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,5,6,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,127,128,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,155,163,164,165,166,167,168,169,253,282,283,287,288,292,323,324,332,338,348,381,411,444", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,370,435,3848,3917,3980,4050,4118,4190,4260,4321,4395,4468,4529,4590,4652,4716,4778,4839,4907,5007,5067,5133,5206,5275,5332,5384,5446,5518,5594,7973,8008,8270,8325,8388,8443,8501,8559,8620,8683,8740,8791,8841,8902,8959,9025,9059,9094,9453,10058,10125,10197,10266,10335,10409,10481,15748,17357,17474,17675,17785,17986,20448,20520,20891,21094,21395,23126,24126,24808", "endLines": "2,3,4,5,6,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,127,128,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,155,163,164,165,166,167,168,169,253,282,286,287,291,292,323,324,337,347,380,401,443,449", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,430,496,3912,3975,4045,4113,4185,4255,4316,4390,4463,4524,4585,4647,4711,4773,4834,4902,5002,5062,5128,5201,5270,5327,5379,5441,5513,5589,5654,8003,8038,8320,8383,8438,8496,8554,8615,8678,8735,8786,8836,8897,8954,9020,9054,9089,9124,9518,10120,10192,10261,10330,10404,10476,10564,15814,17469,17670,17780,17981,18110,20515,20582,21089,21390,23121,23802,24803,24970"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/ea443b1b9bd80f08900abef66e3f9152/transformed/activity-1.8.2/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "131,150", "startColumns": "4,4", "startOffsets": "8163,9172", "endColumns": "41,59", "endOffsets": "8200,9227"}}, {"source": "/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/res/values/colors.xml", "from": {"startLines": "48,44,45,57,58,56,49,46,47,33,35,37,39,51,53,41,43,54,55,32,34,36,38,50,52,40,42,19,15,16,28,29,27,20,17,18,4,6,8,10,22,24,12,14,25,26,3,5,7,9,21,23,11,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2816,2578,2632,3360,3425,3297,2875,2695,2751,1884,2007,2132,2259,2992,3113,2385,2510,3178,3234,1828,1942,2074,2192,2936,3050,2328,2444,1102,860,915,1655,1721,1591,1162,979,1036,155,280,407,536,1281,1404,664,791,1470,1527,98,214,348,468,1224,1340,606,724", "endColumns": "58,53,62,64,62,62,60,55,64,57,66,59,68,57,64,58,67,55,62,55,64,57,66,55,62,56,65,59,54,63,65,63,63,61,56,65,58,67,60,69,58,65,59,68,56,63,56,65,58,67,56,63,57,66", "endOffsets": "2870,2627,2690,3420,3483,3355,2931,2746,2811,1937,2069,2187,2323,3045,3173,2439,2573,3229,3292,1879,2002,2127,2254,2987,3108,2380,2505,1157,910,974,1716,1780,1650,1219,1031,1097,209,343,463,601,1335,1465,719,855,1522,1586,150,275,402,531,1276,1399,659,786"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "501,560,614,677,742,805,868,929,985,1050,1108,1175,1235,1304,1362,1427,1486,1554,1610,1673,1729,1794,1852,1919,1975,2038,2095,2161,2221,2276,2340,2406,2470,2534,2596,2653,2719,2778,2846,2907,2977,3036,3102,3162,3231,3288,3352,3409,3475,3534,3602,3659,3723,3781", "endColumns": "58,53,62,64,62,62,60,55,64,57,66,59,68,57,64,58,67,55,62,55,64,57,66,55,62,56,65,59,54,63,65,63,63,61,56,65,58,67,60,69,58,65,59,68,56,63,56,65,58,67,56,63,57,66", "endOffsets": "555,609,672,737,800,863,924,980,1045,1103,1170,1230,1299,1357,1422,1481,1549,1605,1668,1724,1789,1847,1914,1970,2033,2090,2156,2216,2271,2335,2401,2465,2529,2591,2648,2714,2773,2841,2902,2972,3031,3097,3157,3226,3283,3347,3404,3470,3529,3597,3654,3718,3776,3843"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/383cc2c059ec4292455777a9c87f5065/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "152", "startColumns": "4", "startOffsets": "9286", "endColumns": "49", "endOffsets": "9331"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/91e25bc61d0fccc1b951cbb8bb3e9611/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "149", "startColumns": "4", "startOffsets": "9129", "endColumns": "42", "endOffsets": "9167"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/d5819f9fe65f0ef633cacedd9130b31c/transformed/fragment-1.5.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "123,132,153,402,407", "startColumns": "4,4,4,4,4", "startOffsets": "7756,8205,9336,23807,23977", "endLines": "123,132,153,406,410", "endColumns": "56,64,63,24,24", "endOffsets": "7808,8265,9395,23972,24121"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/200689553ca1d85e425283475c4db55a/transformed/navigation-common-2.7.5/res/values/values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "450,463,469,475,484", "startColumns": "4,4,4,4,4", "startOffsets": "24975,25614,25858,26105,26468", "endLines": "462,468,474,477,488", "endColumns": "24,24,24,24,24", "endOffsets": "25609,25853,26100,26233,26645"}}, {"source": "/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/res/values/themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "139", "endLines": "32", "endColumns": "12", "endOffsets": "2502"}, "to": {"startLines": "293", "startColumns": "4", "startOffsets": "18115", "endLines": "322", "endColumns": "12", "endOffsets": "20443"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/98541b0efff9d81634bbda892da35399/transformed/savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "151", "startColumns": "4", "startOffsets": "9232", "endColumns": "53", "endOffsets": "9281"}}, {"source": "/Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/res/values/strings.xml", "from": {"startLines": "13,1,37,21,8,12,6,35,24,29,26,30,25,31,27,28,11,9,34,4,38,19,7,10,18,20,5,36,15,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "571,16,1733,894,276,501,190,1653,970,1357,1113,1423,1038,1498,1187,1279,440,338,1606,104,1775,808,230,386,762,850,144,1693,689,645", "endColumns": "73,54,41,37,61,69,39,39,67,65,73,74,74,81,91,77,60,47,46,39,33,41,45,53,45,43,45,39,43,43", "endOffsets": "640,66,1770,927,333,566,225,1688,1033,1418,1182,1493,1108,1575,1274,1352,496,381,1648,139,1804,845,271,435,803,889,185,1728,728,684"}, "to": {"startLines": "156,158,170,171,175,214,216,217,218,219,220,221,222,223,224,225,227,228,231,232,237,239,240,241,242,243,246,247,250,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9523,9680,10569,10611,10816,13625,13753,13793,13833,13901,13967,14041,14116,14191,14273,14365,14489,14550,14710,14757,15018,15086,15128,15174,15228,15274,15422,15468,15607,15651", "endColumns": "73,54,41,37,61,69,39,39,67,65,73,74,74,81,91,77,60,47,46,39,33,41,45,53,45,43,45,39,43,43", "endOffsets": "9592,9730,10606,10644,10873,13690,13788,13828,13896,13962,14036,14111,14186,14268,14360,14438,14545,14593,14752,14792,15047,15123,15169,15223,15269,15313,15463,15503,15646,15690"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/fae82ac325c7c1a20d3c61c3ee836a79/transformed/ui-release/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,124,125,154,172,173,211,212,215,229,230,234,235,236,238,244,245,249,255,256,257,272,275,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5659,5718,5777,5837,5897,5957,6017,6077,6137,6197,6257,6317,6377,6436,6496,6556,6616,6676,6736,6796,6856,6916,6976,7036,7095,7155,7215,7274,7333,7392,7451,7510,7569,7643,7701,7813,7864,9400,10649,10714,13414,13480,13695,14598,14650,14866,14928,14982,15052,15318,15368,15561,15887,15934,15970,16939,17051,17162", "endLines": "88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,124,125,154,172,173,211,212,215,229,230,234,235,236,238,244,245,249,255,256,257,274,277,281", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "5713,5772,5832,5892,5952,6012,6072,6132,6192,6252,6312,6372,6431,6491,6551,6611,6671,6731,6791,6851,6911,6971,7031,7090,7150,7210,7269,7328,7387,7446,7505,7564,7638,7696,7751,7859,7914,9448,10709,10763,13475,13576,13748,14645,14705,14923,14977,15013,15081,15363,15417,15602,15929,15965,16055,17046,17157,17352"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/05e4a878ea96dbbd82d922fac59375c5/transformed/startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "157", "startColumns": "4", "startOffsets": "9597", "endColumns": "82", "endOffsets": "9675"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/8d0f9af2c9eacab5ec3874b00081c404/transformed/navigation-runtime-2.7.5/res/values/values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "129,325,478,481", "startColumns": "4,4,4,4", "startOffsets": "8043,20587,26238,26353", "endLines": "129,331,480,483", "endColumns": "52,24,24,24", "endOffsets": "8091,20886,26348,26463"}}]}]}
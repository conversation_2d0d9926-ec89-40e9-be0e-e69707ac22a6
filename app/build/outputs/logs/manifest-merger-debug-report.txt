-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:2:1-35:12
INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:2:1-35:12
INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:2:1-35:12
INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:2:1-35:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/c915505b9e7111742e9c3b31e692852d/transformed/hilt-navigation-compose-1.1.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5cb11b73d32de071b4a905090d7d7b2/transformed/hilt-navigation-1.1.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/200689553ca1d85e425283475c4db55a/transformed/navigation-common-2.7.5/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/8d0f9af2c9eacab5ec3874b00081c404/transformed/navigation-runtime-2.7.5/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa1d4697415e2e701d678e10a799d053/transformed/navigation-common-ktx-2.7.5/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/b362f4359132b97388deee7fd31e3300/transformed/navigation-runtime-ktx-2.7.5/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/8eee98fab7f16dd386485c9dc864844c/transformed/navigation-compose-2.7.5/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3:1.1.2] /Users/<USER>/.gradle/caches/8.12/transforms/ed7ef9edb6e585a5acd6a07df8623d40/transformed/material3-1.1.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/957b626515a1cf2e98e77a64111046e8/transformed/material-ripple-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ce78b98d0e887fa2bf5aac15b2a14d84/transformed/material-icons-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/4c60fb915d5252d849c0ce495b38b5a3/transformed/material-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/d150f22b55e3c21c19450b2df70ba72f/transformed/animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/2a3011c68404870013e989addd58a01e/transformed/animation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/2c7903e14347891f3d780efbe7913df8/transformed/foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/846d9cbf778606d3b49ccba7806c0b6a/transformed/foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ae055542d0d39a1384106bcad0552191/transformed/ui-tooling-data-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9861e1c9271ab2b31658656808899f31/transformed/ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ebe367e1dfa464eecd763249df8bd995/transformed/ui-geometry-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/6f617e0284fed46a7af725beea189bf2/transformed/ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/4f9e67d120b50128c229be9614e7802d/transformed/ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/7c445d1d3f5bbb0f221b6d389ef4becc/transformed/ui-tooling-preview-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9c5dd19ac90c00c5bd54dc90759228a9/transformed/ui-tooling-release/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/0d71cb13ab664c9c2b1e15539f29e935/transformed/ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/dd4d593c48c84c200722ed24578b24c2/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:17:1-28:12
MERGED from [com.google.dagger:hilt-android:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/3ff561d2378cbe9ebd5b7b8c9cf89469/transformed/hilt-android-2.48/AndroidManifest.xml:16:1-19:12
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/d5819f9fe65f0ef633cacedd9130b31c/transformed/fragment-1.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/ea443b1b9bd80f08900abef66e3f9152/transformed/activity-1.8.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/e95b501e5f88e7284c693e9e697f927c/transformed/activity-ktx-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/30b44fc5a0202c0b127d10eae00d1caa/transformed/activity-compose-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/51df7b7a3cb59790307ed4e1f1d51af9/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/98541b0efff9d81634bbda892da35399/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0da0f5a2425f439bc21970dac1c080/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/33d938cadd72135298bf5962613ae2f8/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/ff719d039b4b391f137a2363cc092aba/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e6065d74760e5e947cead3bb9f921f3f/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/9402ceec80a9dfc6f6b7fbf7236379d4/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1ae56b19ac7fb99f9d0bb45c584a686c/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/383cc2c059ec4292455777a9c87f5065/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/91e25bc61d0fccc1b951cbb8bb3e9611/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/4727cb3d86a95fbb1252a61c72ea80a8/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/3e9b1bbd7503eeb1a7ae3ffcf71bc097/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/fae82ac325c7c1a20d3c61c3ee836a79/transformed/ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f7724df13fa579c7c218c1e51f66d33e/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3ee5f0bfb87cd4806afc8c405f4663e7/transformed/autofill-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1365b058cfeb39b4844ba362e49b16ae/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f3c98bae8bedbff9bdfb42c4e1cc54f0/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/71ba66281c49aae0414fc31fef175f6b/transformed/core-ktx-1.12.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/ddec48ea6f5a63a68f2c95e7d97cce5c/transformed/room-ktx-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/f00c54e6344ad4d55dcef29dfa453208/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/17cecbf94154df6a8120c26a4934e5df/transformed/sqlite-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/cc561f4a1f0631f361d094c7e3ff0e17/transformed/runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b39bd461bc002adcac54d2753e470564/transformed/runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/35e1f77e9a3adaf79cc0e58f2d0a945b/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.12/transforms/4c9fd80d940484a410893af32b99c3bc/transformed/security-crypto-1.1.0-alpha06/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5cf21cc632411b4c1e5d24dc7dc514c1/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/03eedac29184ce3e67bf6613682f7382/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/205255f3e58dfea0a9b24cad055b83e2/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/05e4a878ea96dbbd82d922fac59375c5/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e9d7317ab3161b4f9059b9e104808e87/transformed/tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/3915cfd7f4e8abcee6f14e2d5f958519/transformed/dagger-lint-aar-2.48/AndroidManifest.xml:16:1-19:12
	package
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:4:5-38
		INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:7:5-67
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:7:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:8:5-79
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:8:22-76
application
ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:10:5-33:19
INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:10:5-33:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9c5dd19ac90c00c5bd54dc90759228a9/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9c5dd19ac90c00c5bd54dc90759228a9/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/dd4d593c48c84c200722ed24578b24c2/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/dd4d593c48c84c200722ed24578b24c2/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/03eedac29184ce3e67bf6613682f7382/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/03eedac29184ce3e67bf6613682f7382/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/05e4a878ea96dbbd82d922fac59375c5/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/05e4a878ea96dbbd82d922fac59375c5/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:18:9-35
	android:label
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:16:9-41
	android:fullBackupContent
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:14:9-54
	android:roundIcon
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:17:9-54
	tools:targetApi
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:21:9-29
	android:icon
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:15:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:12:9-35
	android:theme
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:19:9-56
	android:dataExtractionRules
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:13:9-65
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:20:9-45
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:11:9-46
activity#com.saiharvest.orderapp.MainActivity
ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:23:9-32:20
	android:label
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:26:13-45
	android:exported
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:25:13-36
	android:theme
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:27:13-60
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:24:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:28:13-31:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:29:17-69
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:29:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:30:17-77
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml:30:27-74
uses-sdk
INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/c915505b9e7111742e9c3b31e692852d/transformed/hilt-navigation-compose-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/c915505b9e7111742e9c3b31e692852d/transformed/hilt-navigation-compose-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5cb11b73d32de071b4a905090d7d7b2/transformed/hilt-navigation-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5cb11b73d32de071b4a905090d7d7b2/transformed/hilt-navigation-1.1.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/200689553ca1d85e425283475c4db55a/transformed/navigation-common-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/200689553ca1d85e425283475c4db55a/transformed/navigation-common-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/8d0f9af2c9eacab5ec3874b00081c404/transformed/navigation-runtime-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/8d0f9af2c9eacab5ec3874b00081c404/transformed/navigation-runtime-2.7.5/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa1d4697415e2e701d678e10a799d053/transformed/navigation-common-ktx-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa1d4697415e2e701d678e10a799d053/transformed/navigation-common-ktx-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/b362f4359132b97388deee7fd31e3300/transformed/navigation-runtime-ktx-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/b362f4359132b97388deee7fd31e3300/transformed/navigation-runtime-ktx-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/8eee98fab7f16dd386485c9dc864844c/transformed/navigation-compose-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] /Users/<USER>/.gradle/caches/8.12/transforms/8eee98fab7f16dd386485c9dc864844c/transformed/navigation-compose-2.7.5/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] /Users/<USER>/.gradle/caches/8.12/transforms/ed7ef9edb6e585a5acd6a07df8623d40/transformed/material3-1.1.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] /Users/<USER>/.gradle/caches/8.12/transforms/ed7ef9edb6e585a5acd6a07df8623d40/transformed/material3-1.1.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/957b626515a1cf2e98e77a64111046e8/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/957b626515a1cf2e98e77a64111046e8/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ce78b98d0e887fa2bf5aac15b2a14d84/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ce78b98d0e887fa2bf5aac15b2a14d84/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/4c60fb915d5252d849c0ce495b38b5a3/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/4c60fb915d5252d849c0ce495b38b5a3/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/d150f22b55e3c21c19450b2df70ba72f/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/d150f22b55e3c21c19450b2df70ba72f/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/2a3011c68404870013e989addd58a01e/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/2a3011c68404870013e989addd58a01e/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/2c7903e14347891f3d780efbe7913df8/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/2c7903e14347891f3d780efbe7913df8/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/846d9cbf778606d3b49ccba7806c0b6a/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/846d9cbf778606d3b49ccba7806c0b6a/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ae055542d0d39a1384106bcad0552191/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ae055542d0d39a1384106bcad0552191/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9861e1c9271ab2b31658656808899f31/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9861e1c9271ab2b31658656808899f31/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ebe367e1dfa464eecd763249df8bd995/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/ebe367e1dfa464eecd763249df8bd995/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/6f617e0284fed46a7af725beea189bf2/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/6f617e0284fed46a7af725beea189bf2/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/4f9e67d120b50128c229be9614e7802d/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/4f9e67d120b50128c229be9614e7802d/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/7c445d1d3f5bbb0f221b6d389ef4becc/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/7c445d1d3f5bbb0f221b6d389ef4becc/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9c5dd19ac90c00c5bd54dc90759228a9/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9c5dd19ac90c00c5bd54dc90759228a9/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/0d71cb13ab664c9c2b1e15539f29e935/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/0d71cb13ab664c9c2b1e15539f29e935/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/dd4d593c48c84c200722ed24578b24c2/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/dd4d593c48c84c200722ed24578b24c2/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/3ff561d2378cbe9ebd5b7b8c9cf89469/transformed/hilt-android-2.48/AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/3ff561d2378cbe9ebd5b7b8c9cf89469/transformed/hilt-android-2.48/AndroidManifest.xml:18:3-42
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/d5819f9fe65f0ef633cacedd9130b31c/transformed/fragment-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/d5819f9fe65f0ef633cacedd9130b31c/transformed/fragment-1.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/ea443b1b9bd80f08900abef66e3f9152/transformed/activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/ea443b1b9bd80f08900abef66e3f9152/transformed/activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/e95b501e5f88e7284c693e9e697f927c/transformed/activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/e95b501e5f88e7284c693e9e697f927c/transformed/activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/30b44fc5a0202c0b127d10eae00d1caa/transformed/activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] /Users/<USER>/.gradle/caches/8.12/transforms/30b44fc5a0202c0b127d10eae00d1caa/transformed/activity-compose-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/51df7b7a3cb59790307ed4e1f1d51af9/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/51df7b7a3cb59790307ed4e1f1d51af9/transformed/savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/98541b0efff9d81634bbda892da35399/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/98541b0efff9d81634bbda892da35399/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0da0f5a2425f439bc21970dac1c080/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0da0f5a2425f439bc21970dac1c080/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/33d938cadd72135298bf5962613ae2f8/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/33d938cadd72135298bf5962613ae2f8/transformed/lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/ff719d039b4b391f137a2363cc092aba/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/ff719d039b4b391f137a2363cc092aba/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e6065d74760e5e947cead3bb9f921f3f/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e6065d74760e5e947cead3bb9f921f3f/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/9402ceec80a9dfc6f6b7fbf7236379d4/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/9402ceec80a9dfc6f6b7fbf7236379d4/transformed/lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1ae56b19ac7fb99f9d0bb45c584a686c/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1ae56b19ac7fb99f9d0bb45c584a686c/transformed/lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/383cc2c059ec4292455777a9c87f5065/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/383cc2c059ec4292455777a9c87f5065/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/91e25bc61d0fccc1b951cbb8bb3e9611/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/91e25bc61d0fccc1b951cbb8bb3e9611/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/4727cb3d86a95fbb1252a61c72ea80a8/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/4727cb3d86a95fbb1252a61c72ea80a8/transformed/lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/3e9b1bbd7503eeb1a7ae3ffcf71bc097/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/3e9b1bbd7503eeb1a7ae3ffcf71bc097/transformed/lifecycle-viewmodel-compose-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/fae82ac325c7c1a20d3c61c3ee836a79/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/fae82ac325c7c1a20d3c61c3ee836a79/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f7724df13fa579c7c218c1e51f66d33e/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f7724df13fa579c7c218c1e51f66d33e/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3ee5f0bfb87cd4806afc8c405f4663e7/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3ee5f0bfb87cd4806afc8c405f4663e7/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1365b058cfeb39b4844ba362e49b16ae/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1365b058cfeb39b4844ba362e49b16ae/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f3c98bae8bedbff9bdfb42c4e1cc54f0/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f3c98bae8bedbff9bdfb42c4e1cc54f0/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/71ba66281c49aae0414fc31fef175f6b/transformed/core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/71ba66281c49aae0414fc31fef175f6b/transformed/core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/ddec48ea6f5a63a68f2c95e7d97cce5c/transformed/room-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/ddec48ea6f5a63a68f2c95e7d97cce5c/transformed/room-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/f00c54e6344ad4d55dcef29dfa453208/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/f00c54e6344ad4d55dcef29dfa453208/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/17cecbf94154df6a8120c26a4934e5df/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/17cecbf94154df6a8120c26a4934e5df/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/cc561f4a1f0631f361d094c7e3ff0e17/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/cc561f4a1f0631f361d094c7e3ff0e17/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b39bd461bc002adcac54d2753e470564/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/b39bd461bc002adcac54d2753e470564/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/35e1f77e9a3adaf79cc0e58f2d0a945b/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/35e1f77e9a3adaf79cc0e58f2d0a945b/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.12/transforms/4c9fd80d940484a410893af32b99c3bc/transformed/security-crypto-1.1.0-alpha06/AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] /Users/<USER>/.gradle/caches/8.12/transforms/4c9fd80d940484a410893af32b99c3bc/transformed/security-crypto-1.1.0-alpha06/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5cf21cc632411b4c1e5d24dc7dc514c1/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5cf21cc632411b4c1e5d24dc7dc514c1/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/03eedac29184ce3e67bf6613682f7382/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/03eedac29184ce3e67bf6613682f7382/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/205255f3e58dfea0a9b24cad055b83e2/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/205255f3e58dfea0a9b24cad055b83e2/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/05e4a878ea96dbbd82d922fac59375c5/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/05e4a878ea96dbbd82d922fac59375c5/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e9d7317ab3161b4f9059b9e104808e87/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e9d7317ab3161b4f9059b9e104808e87/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:dagger-lint-aar:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/3915cfd7f4e8abcee6f14e2d5f958519/transformed/dagger-lint-aar-2.48/AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] /Users/<USER>/.gradle/caches/8.12/transforms/3915cfd7f4e8abcee6f14e2d5f958519/transformed/dagger-lint-aar-2.48/AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/SaiHarvestFoods/app/src/main/AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9c5dd19ac90c00c5bd54dc90759228a9/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9c5dd19ac90c00c5bd54dc90759228a9/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/9c5dd19ac90c00c5bd54dc90759228a9/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/dd4d593c48c84c200722ed24578b24c2/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/dd4d593c48c84c200722ed24578b24c2/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] /Users/<USER>/.gradle/caches/8.12/transforms/dd4d593c48c84c200722ed24578b24c2/transformed/ui-test-manifest-1.5.4/AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/05e4a878ea96dbbd82d922fac59375c5/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/05e4a878ea96dbbd82d922fac59375c5/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a17e026ddca6ea900d774e51716a971/transformed/emoji2-1.4.0/AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2091f1e1044706603a3324120186a4b/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
permission#com.saiharvest.orderapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
uses-permission#com.saiharvest.orderapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/0e0a26c195feec5c5a7a32b880b072eb/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/c014934748bf4631eca1ec227db504e4/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4cd43cfcdea9000013ab9b71fa94c3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92

package com.saiharvest.orderapp

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.compose.rememberNavController
import com.saiharvest.orderapp.domain.usecase.GetCurrentUserUseCase
import com.saiharvest.orderapp.presentation.navigation.AppNavigation
import com.saiharvest.orderapp.presentation.navigation.Screen
import com.saiharvest.orderapp.presentation.ui.theme.SaiHarvestOrderAppTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    @Inject
    lateinit var getCurrentUserUseCase: GetCurrentUserUseCase
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            SaiHarvestOrderAppTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val navController = rememberNavController()
                    var startDestination by remember { mutableStateOf<String?>(null) }
                    val scope = rememberCoroutineScope()
                    
                    // Check if user is already logged in
                    LaunchedEffect(Unit) {
                        scope.launch {
                            val currentUser = getCurrentUserUseCase()
                            startDestination = if (currentUser != null) {
                                Screen.Products.route
                            } else {
                                Screen.Login.route
                            }
                        }
                    }
                    
                    // Show navigation only when start destination is determined
                    startDestination?.let { destination ->
                        AppNavigation(
                            navController = navController,
                            startDestination = destination
                        )
                    }
                }
            }
        }
    }
}

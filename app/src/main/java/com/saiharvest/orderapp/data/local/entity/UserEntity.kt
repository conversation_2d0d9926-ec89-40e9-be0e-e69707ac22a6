package com.saiharvest.orderapp.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.saiharvest.orderapp.domain.model.User

@Entity(tableName = "users")
data class UserEntity(
    @PrimaryKey
    val id: String,
    val email: String,
    val fullName: String,
    val phoneNumber: String,
    val isEmailVerified: <PERSON>olean,
    val createdAt: Long,
    val updatedAt: Long
)

fun UserEntity.toDomain(): User {
    return User(
        id = id,
        email = email,
        fullName = fullName,
        phoneNumber = phoneNumber,
        isEmailVerified = isEmailVerified,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

fun User.toEntity(): UserEntity {
    return UserEntity(
        id = id,
        email = email,
        fullName = fullName,
        phoneNumber = phoneNumber,
        isEmailVerified = isEmailVerified,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

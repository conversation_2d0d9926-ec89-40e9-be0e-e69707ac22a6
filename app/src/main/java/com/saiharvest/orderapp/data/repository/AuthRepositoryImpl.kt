package com.saiharvest.orderapp.data.repository

import com.saiharvest.orderapp.data.local.dao.UserDao
import com.saiharvest.orderapp.data.local.entity.toDomain
import com.saiharvest.orderapp.data.local.entity.toEntity
import com.saiharvest.orderapp.data.local.preferences.SecurePreferencesManager
import com.saiharvest.orderapp.domain.model.AuthResult
import com.saiharvest.orderapp.domain.model.LoginRequest
import com.saiharvest.orderapp.domain.model.RegisterRequest
import com.saiharvest.orderapp.domain.model.User
import com.saiharvest.orderapp.domain.repository.AuthRepository
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepositoryImpl @Inject constructor(
    private val userDao: UserDao,
    private val securePreferencesManager: SecurePreferencesManager
) : AuthRepository {
    
    override suspend fun login(request: LoginRequest): Flow<AuthResult> = flow {
        emit(AuthResult.Loading)
        
        try {
            // Simulate network delay
            delay(1000)
            
            // For now, simulate login with mock validation
            // In real implementation, this would call API
            if (request.email.isNotEmpty() && request.password.length >= 6) {
                val existingUser = userDao.getUserByEmail(request.email)
                
                if (existingUser != null) {
                    val user = existingUser.toDomain()
                    saveUserSession(user)
                    emit(AuthResult.Success(user))
                } else {
                    emit(AuthResult.Error("User not found. Please register first."))
                }
            } else {
                emit(AuthResult.Error("Invalid email or password"))
            }
        } catch (e: Exception) {
            emit(AuthResult.Error(e.message ?: "Login failed"))
        }
    }
    
    override suspend fun register(request: RegisterRequest): Flow<AuthResult> = flow {
        emit(AuthResult.Loading)
        
        try {
            // Simulate network delay
            delay(1000)
            
            // Check if user already exists
            val existingUser = userDao.getUserByEmail(request.email)
            if (existingUser != null) {
                emit(AuthResult.Error("User with this email already exists"))
                return@flow
            }
            
            // Create new user
            val newUser = User(
                id = UUID.randomUUID().toString(),
                email = request.email,
                fullName = request.fullName,
                phoneNumber = request.phoneNumber,
                isEmailVerified = false,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )
            
            // Save to local database
            userDao.insertUser(newUser.toEntity())
            saveUserSession(newUser)
            
            emit(AuthResult.Success(newUser))
        } catch (e: Exception) {
            emit(AuthResult.Error(e.message ?: "Registration failed"))
        }
    }
    
    override suspend fun logout() {
        clearUserSession()
    }
    
    override suspend fun getCurrentUser(): User? {
        val userId = securePreferencesManager.getUserId()
        return userId?.let { userDao.getUserById(it)?.toDomain() }
    }
    
    override suspend fun isUserLoggedIn(): Boolean {
        return securePreferencesManager.isUserLoggedIn()
    }
    
    override suspend fun saveUserSession(user: User) {
        securePreferencesManager.saveUserSession(user.id, user.email)
    }
    
    override suspend fun clearUserSession() {
        securePreferencesManager.clearUserSession()
    }
}

package com.saiharvest.orderapp.domain.repository

import com.saiharvest.orderapp.domain.model.AuthResult
import com.saiharvest.orderapp.domain.model.LoginRequest
import com.saiharvest.orderapp.domain.model.RegisterRequest
import com.saiharvest.orderapp.domain.model.User
import kotlinx.coroutines.flow.Flow

interface AuthRepository {
    suspend fun login(request: LoginRequest): Flow<AuthResult>
    suspend fun register(request: RegisterRequest): Flow<AuthResult>
    suspend fun logout()
    suspend fun getCurrentUser(): User?
    suspend fun isUserLoggedIn(): Boolean
    suspend fun saveUserSession(user: User)
    suspend fun clearUserSession()
}

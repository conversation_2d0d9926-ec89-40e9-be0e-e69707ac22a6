package com.saiharvest.orderapp.domain.usecase

import com.saiharvest.orderapp.domain.model.AuthResult
import com.saiharvest.orderapp.domain.model.LoginRequest
import com.saiharvest.orderapp.domain.repository.AuthRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class LoginUseCase @Inject constructor(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(email: String, password: String): Flow<AuthResult> {
        return authRepository.login(LoginRequest(email, password))
    }
}

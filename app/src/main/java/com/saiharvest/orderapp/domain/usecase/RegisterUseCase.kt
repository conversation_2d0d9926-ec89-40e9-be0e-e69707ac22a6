package com.saiharvest.orderapp.domain.usecase

import com.saiharvest.orderapp.domain.model.AuthResult
import com.saiharvest.orderapp.domain.model.RegisterRequest
import com.saiharvest.orderapp.domain.repository.AuthRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class RegisterUseCase @Inject constructor(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(
        email: String,
        password: String,
        fullName: String,
        phoneNumber: String
    ): Flow<AuthResult> {
        return authRepository.register(
            RegisterRequest(
                email = email,
                password = password,
                fullName = fullName,
                phoneNumber = phoneNumber
            )
        )
    }
}

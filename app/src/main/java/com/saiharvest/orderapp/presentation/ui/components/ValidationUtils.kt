package com.saiharvest.orderapp.presentation.ui.components

import android.util.Patterns

object ValidationUtils {
    
    fun validateEmail(email: String): String? {
        return when {
            email.isEmpty() -> "Email cannot be empty"
            !Patterns.EMAIL_ADDRESS.matcher(email).matches() -> "Please enter a valid email"
            else -> null
        }
    }
    
    fun validatePassword(password: String): String? {
        return when {
            password.isEmpty() -> "Password cannot be empty"
            password.length < 6 -> "Password must be at least 6 characters"
            else -> null
        }
    }
    
    fun validateConfirmPassword(password: String, confirmPassword: String): String? {
        return when {
            confirmPassword.isEmpty() -> "Please confirm your password"
            password != confirmPassword -> "Passwords do not match"
            else -> null
        }
    }
    
    fun validateName(name: String): String? {
        return when {
            name.isEmpty() -> "Name cannot be empty"
            name.length < 2 -> "Name must be at least 2 characters"
            else -> null
        }
    }
    
    fun validatePhoneNumber(phoneNumber: String): String? {
        return when {
            phoneNumber.isEmpty() -> "Phone number cannot be empty"
            phoneNumber.length < 10 -> "Please enter a valid phone number"
            !phoneNumber.all { it.isDigit() || it == '+' || it == '-' || it == ' ' || it == '(' || it == ')' } -> 
                "Please enter a valid phone number"
            else -> null
        }
    }
}

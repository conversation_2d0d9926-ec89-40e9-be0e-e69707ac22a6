package com.saiharvest.orderapp.presentation.ui.theme

import androidx.compose.ui.graphics.Color

// Light Theme Colors
val md_theme_light_primary = Color(0xFF006A6B)
val md_theme_light_onPrimary = Color(0xFFFFFFFF)
val md_theme_light_primaryContainer = Color(0xFF6FF7F8)
val md_theme_light_onPrimaryContainer = Color(0xFF002020)
val md_theme_light_secondary = Color(0xFF4A6363)
val md_theme_light_onSecondary = Color(0xFFFFFFFF)
val md_theme_light_secondaryContainer = Color(0xFFCCE8E7)
val md_theme_light_onSecondaryContainer = Color(0xFF051F1F)
val md_theme_light_tertiary = Color(0xFF456179)
val md_theme_light_onTertiary = Color(0xFFFFFFFF)
val md_theme_light_tertiaryContainer = Color(0xFFCCE5FF)
val md_theme_light_onTertiaryContainer = Color(0xFF001E31)
val md_theme_light_error = Color(0xFFBA1A1A)
val md_theme_light_errorContainer = Color(0xFFFFDAD6)
val md_theme_light_onError = Color(0xFFFFFFFF)
val md_theme_light_onErrorContainer = Color(0xFF410002)
val md_theme_light_background = Color(0xFFFAFDFC)
val md_theme_light_onBackground = Color(0xFF191C1C)
val md_theme_light_surface = Color(0xFFFAFDFC)
val md_theme_light_onSurface = Color(0xFF191C1C)
val md_theme_light_surfaceVariant = Color(0xFFDAE5E4)
val md_theme_light_onSurfaceVariant = Color(0xFF3F4948)
val md_theme_light_outline = Color(0xFF6F7979)
val md_theme_light_inverseOnSurface = Color(0xFFEFF1F0)
val md_theme_light_inverseSurface = Color(0xFF2D3131)
val md_theme_light_inversePrimary = Color(0xFF4DDADB)

// Dark Theme Colors
val md_theme_dark_primary = Color(0xFF4DDADB)
val md_theme_dark_onPrimary = Color(0xFF003738)
val md_theme_dark_primaryContainer = Color(0xFF005051)
val md_theme_dark_onPrimaryContainer = Color(0xFF6FF7F8)
val md_theme_dark_secondary = Color(0xFFB1CCCB)
val md_theme_dark_onSecondary = Color(0xFF1C3535)
val md_theme_dark_secondaryContainer = Color(0xFF334B4B)
val md_theme_dark_onSecondaryContainer = Color(0xFFCCE8E7)
val md_theme_dark_tertiary = Color(0xFFA4C9E8)
val md_theme_dark_onTertiary = Color(0xFF0A344A)
val md_theme_dark_tertiaryContainer = Color(0xFF284B61)
val md_theme_dark_onTertiaryContainer = Color(0xFFCCE5FF)
val md_theme_dark_error = Color(0xFFFFB4AB)
val md_theme_dark_errorContainer = Color(0xFF93000A)
val md_theme_dark_onError = Color(0xFF690005)
val md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val md_theme_dark_background = Color(0xFF0F1414)
val md_theme_dark_onBackground = Color(0xFFDFE3E2)
val md_theme_dark_surface = Color(0xFF0F1414)
val md_theme_dark_onSurface = Color(0xFFDFE3E2)
val md_theme_dark_surfaceVariant = Color(0xFF3F4948)
val md_theme_dark_onSurfaceVariant = Color(0xFFBEC9C8)
val md_theme_dark_outline = Color(0xFF899392)
val md_theme_dark_inverseOnSurface = Color(0xFF2D3131)
val md_theme_dark_inverseSurface = Color(0xFFDFE3E2)
val md_theme_dark_inversePrimary = Color(0xFF006A6B)

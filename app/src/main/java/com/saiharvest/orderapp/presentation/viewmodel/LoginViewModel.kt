package com.saiharvest.orderapp.presentation.viewmodel

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.saiharvest.orderapp.domain.model.AuthResult
import com.saiharvest.orderapp.domain.usecase.LoginUseCase
import com.saiharvest.orderapp.presentation.ui.components.ValidationUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

data class LoginUiState(
    val email: String = "",
    val password: String = "",
    val emailError: String? = null,
    val passwordError: String? = null,
    val isLoading: Boolean = false,
    val loginError: String? = null,
    val isLoginSuccessful: Boolean = false
)

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val loginUseCase: LoginUseCase
) : ViewModel() {
    
    private val _uiState = mutableStateOf(LoginUiState())
    val uiState: State<LoginUiState> = _uiState
    
    fun onEmailChanged(email: String) {
        _uiState.value = _uiState.value.copy(
            email = email,
            emailError = null,
            loginError = null
        )
    }
    
    fun onPasswordChanged(password: String) {
        _uiState.value = _uiState.value.copy(
            password = password,
            passwordError = null,
            loginError = null
        )
    }
    
    fun login() {
        val currentState = _uiState.value
        
        // Validate inputs
        val emailError = ValidationUtils.validateEmail(currentState.email)
        val passwordError = ValidationUtils.validatePassword(currentState.password)
        
        if (emailError != null || passwordError != null) {
            _uiState.value = currentState.copy(
                emailError = emailError,
                passwordError = passwordError
            )
            return
        }
        
        // Proceed with login
        viewModelScope.launch {
            loginUseCase(currentState.email, currentState.password).collect { result ->
                when (result) {
                    is AuthResult.Loading -> {
                        _uiState.value = currentState.copy(
                            isLoading = true,
                            loginError = null
                        )
                    }
                    is AuthResult.Success -> {
                        _uiState.value = currentState.copy(
                            isLoading = false,
                            isLoginSuccessful = true,
                            loginError = null
                        )
                    }
                    is AuthResult.Error -> {
                        _uiState.value = currentState.copy(
                            isLoading = false,
                            loginError = result.message
                        )
                    }
                }
            }
        }
    }
    
    fun clearLoginSuccess() {
        _uiState.value = _uiState.value.copy(isLoginSuccessful = false)
    }
}

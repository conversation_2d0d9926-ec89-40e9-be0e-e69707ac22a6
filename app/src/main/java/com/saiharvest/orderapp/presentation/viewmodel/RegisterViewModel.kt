package com.saiharvest.orderapp.presentation.viewmodel

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.saiharvest.orderapp.domain.model.AuthResult
import com.saiharvest.orderapp.domain.usecase.RegisterUseCase
import com.saiharvest.orderapp.presentation.ui.components.ValidationUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

data class RegisterUiState(
    val email: String = "",
    val password: String = "",
    val confirmPassword: String = "",
    val fullName: String = "",
    val phoneNumber: String = "",
    val emailError: String? = null,
    val passwordError: String? = null,
    val confirmPasswordError: String? = null,
    val fullNameError: String? = null,
    val phoneNumberError: String? = null,
    val isLoading: Boolean = false,
    val registerError: String? = null,
    val isRegistrationSuccessful: Boolean = false
)

@HiltViewModel
class RegisterViewModel @Inject constructor(
    private val registerUseCase: RegisterUseCase
) : ViewModel() {
    
    private val _uiState = mutableStateOf(RegisterUiState())
    val uiState: State<RegisterUiState> = _uiState
    
    fun onEmailChanged(email: String) {
        _uiState.value = _uiState.value.copy(
            email = email,
            emailError = null,
            registerError = null
        )
    }
    
    fun onPasswordChanged(password: String) {
        _uiState.value = _uiState.value.copy(
            password = password,
            passwordError = null,
            registerError = null
        )
    }
    
    fun onConfirmPasswordChanged(confirmPassword: String) {
        _uiState.value = _uiState.value.copy(
            confirmPassword = confirmPassword,
            confirmPasswordError = null,
            registerError = null
        )
    }
    
    fun onFullNameChanged(fullName: String) {
        _uiState.value = _uiState.value.copy(
            fullName = fullName,
            fullNameError = null,
            registerError = null
        )
    }
    
    fun onPhoneNumberChanged(phoneNumber: String) {
        _uiState.value = _uiState.value.copy(
            phoneNumber = phoneNumber,
            phoneNumberError = null,
            registerError = null
        )
    }
    
    fun register() {
        val currentState = _uiState.value
        
        // Validate inputs
        val emailError = ValidationUtils.validateEmail(currentState.email)
        val passwordError = ValidationUtils.validatePassword(currentState.password)
        val confirmPasswordError = ValidationUtils.validateConfirmPassword(
            currentState.password, 
            currentState.confirmPassword
        )
        val fullNameError = ValidationUtils.validateName(currentState.fullName)
        val phoneNumberError = ValidationUtils.validatePhoneNumber(currentState.phoneNumber)
        
        if (emailError != null || passwordError != null || confirmPasswordError != null || 
            fullNameError != null || phoneNumberError != null) {
            _uiState.value = currentState.copy(
                emailError = emailError,
                passwordError = passwordError,
                confirmPasswordError = confirmPasswordError,
                fullNameError = fullNameError,
                phoneNumberError = phoneNumberError
            )
            return
        }
        
        // Proceed with registration
        viewModelScope.launch {
            registerUseCase(
                currentState.email,
                currentState.password,
                currentState.fullName,
                currentState.phoneNumber
            ).collect { result ->
                when (result) {
                    is AuthResult.Loading -> {
                        _uiState.value = currentState.copy(
                            isLoading = true,
                            registerError = null
                        )
                    }
                    is AuthResult.Success -> {
                        _uiState.value = currentState.copy(
                            isLoading = false,
                            isRegistrationSuccessful = true,
                            registerError = null
                        )
                    }
                    is AuthResult.Error -> {
                        _uiState.value = currentState.copy(
                            isLoading = false,
                            registerError = result.message
                        )
                    }
                }
            }
        }
    }
    
    fun clearRegistrationSuccess() {
        _uiState.value = _uiState.value.copy(isRegistrationSuccessful = false)
    }
}

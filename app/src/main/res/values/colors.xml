<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material 3 Light Theme Colors -->
    <color name="md_theme_light_primary">#006A6B</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_primaryContainer">#6FF7F8</color>
    <color name="md_theme_light_onPrimaryContainer">#002020</color>
    <color name="md_theme_light_secondary">#4A6363</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_secondaryContainer">#CCE8E7</color>
    <color name="md_theme_light_onSecondaryContainer">#051F1F</color>
    <color name="md_theme_light_tertiary">#456179</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_tertiaryContainer">#CCE5FF</color>
    <color name="md_theme_light_onTertiaryContainer">#001E31</color>
    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    <color name="md_theme_light_background">#FAFDFC</color>
    <color name="md_theme_light_onBackground">#191C1C</color>
    <color name="md_theme_light_surface">#FAFDFC</color>
    <color name="md_theme_light_onSurface">#191C1C</color>
    <color name="md_theme_light_surfaceVariant">#DAE5E4</color>
    <color name="md_theme_light_onSurfaceVariant">#3F4948</color>
    <color name="md_theme_light_outline">#6F7979</color>
    <color name="md_theme_light_outlineVariant">#BEC9C8</color>
    <color name="md_theme_light_inverseSurface">#2D3131</color>
    <color name="md_theme_light_inverseOnSurface">#EFF1F0</color>
    <color name="md_theme_light_inversePrimary">#4DDADB</color>

    <!-- Material 3 Dark Theme Colors -->
    <color name="md_theme_dark_primary">#4DDADB</color>
    <color name="md_theme_dark_onPrimary">#003738</color>
    <color name="md_theme_dark_primaryContainer">#005051</color>
    <color name="md_theme_dark_onPrimaryContainer">#6FF7F8</color>
    <color name="md_theme_dark_secondary">#B1CCCB</color>
    <color name="md_theme_dark_onSecondary">#1C3535</color>
    <color name="md_theme_dark_secondaryContainer">#334B4B</color>
    <color name="md_theme_dark_onSecondaryContainer">#CCE8E7</color>
    <color name="md_theme_dark_tertiary">#A4C9E8</color>
    <color name="md_theme_dark_onTertiary">#0A344A</color>
    <color name="md_theme_dark_tertiaryContainer">#284B61</color>
    <color name="md_theme_dark_onTertiaryContainer">#CCE5FF</color>
    <color name="md_theme_dark_error">#FFB4AB</color>
    <color name="md_theme_dark_errorContainer">#93000A</color>
    <color name="md_theme_dark_onError">#690005</color>
    <color name="md_theme_dark_onErrorContainer">#FFDAD6</color>
    <color name="md_theme_dark_background">#0F1414</color>
    <color name="md_theme_dark_onBackground">#DFE3E2</color>
    <color name="md_theme_dark_surface">#0F1414</color>
    <color name="md_theme_dark_onSurface">#DFE3E2</color>
    <color name="md_theme_dark_surfaceVariant">#3F4948</color>
    <color name="md_theme_dark_onSurfaceVariant">#BEC9C8</color>
    <color name="md_theme_dark_outline">#899392</color>
    <color name="md_theme_dark_outlineVariant">#3F4948</color>
    <color name="md_theme_dark_inverseSurface">#DFE3E2</color>
    <color name="md_theme_dark_inverseOnSurface">#2D3131</color>
    <color name="md_theme_dark_inversePrimary">#006A6B</color>
</resources>

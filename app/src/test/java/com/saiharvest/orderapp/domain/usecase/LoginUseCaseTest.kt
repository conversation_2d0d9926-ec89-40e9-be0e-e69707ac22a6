package com.saiharvest.orderapp.domain.usecase

import com.saiharvest.orderapp.domain.model.AuthResult
import com.saiharvest.orderapp.domain.model.LoginRequest
import com.saiharvest.orderapp.domain.model.User
import com.saiharvest.orderapp.domain.repository.AuthRepository
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever

class LoginUseCaseTest {
    
    @Mock
    private lateinit var authRepository: AuthRepository
    
    private lateinit var loginUseCase: LoginUseCase
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        loginUseCase = LoginUseCase(authRepository)
    }
    
    @Test
    fun `login with valid credentials returns success`() = runTest {
        // Given
        val email = "<EMAIL>"
        val password = "password123"
        val user = User(id = "1", email = email, fullName = "Test User")
        val expectedResult = AuthResult.Success(user)
        
        whenever(authRepository.login(LoginRequest(email, password)))
            .thenReturn(flowOf(expectedResult))
        
        // When
        val result = loginUseCase(email, password).toList()
        
        // Then
        assertEquals(1, result.size)
        assertTrue(result[0] is AuthResult.Success)
        assertEquals(user, (result[0] as AuthResult.Success).user)
    }
    
    @Test
    fun `login with invalid credentials returns error`() = runTest {
        // Given
        val email = "<EMAIL>"
        val password = "wrongpassword"
        val expectedResult = AuthResult.Error("Invalid credentials")
        
        whenever(authRepository.login(LoginRequest(email, password)))
            .thenReturn(flowOf(expectedResult))
        
        // When
        val result = loginUseCase(email, password).toList()
        
        // Then
        assertEquals(1, result.size)
        assertTrue(result[0] is AuthResult.Error)
        assertEquals("Invalid credentials", (result[0] as AuthResult.Error).message)
    }
}

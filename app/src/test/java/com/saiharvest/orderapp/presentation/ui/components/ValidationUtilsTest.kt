package com.saiharvest.orderapp.presentation.ui.components

import org.junit.Assert.*
import org.junit.Test

class ValidationUtilsTest {
    
    @Test
    fun `validateEmail with valid email returns null`() {
        val result = ValidationUtils.validateEmail("<EMAIL>")
        assertNull(result)
    }
    
    @Test
    fun `validateEmail with empty email returns error`() {
        val result = ValidationUtils.validateEmail("")
        assertEquals("Email cannot be empty", result)
    }
    
    @Test
    fun `validateEmail with invalid email returns error`() {
        val result = ValidationUtils.validateEmail("invalid-email")
        assertEquals("Please enter a valid email", result)
    }
    
    @Test
    fun `validatePassword with valid password returns null`() {
        val result = ValidationUtils.validatePassword("password123")
        assertNull(result)
    }
    
    @Test
    fun `validatePassword with empty password returns error`() {
        val result = ValidationUtils.validatePassword("")
        assertEquals("Password cannot be empty", result)
    }
    
    @Test
    fun `validatePassword with short password returns error`() {
        val result = ValidationUtils.validatePassword("123")
        assertEquals("Password must be at least 6 characters", result)
    }
    
    @Test
    fun `validateConfirmPassword with matching passwords returns null`() {
        val result = ValidationUtils.validateConfirmPassword("password123", "password123")
        assertNull(result)
    }
    
    @Test
    fun `validateConfirmPassword with non-matching passwords returns error`() {
        val result = ValidationUtils.validateConfirmPassword("password123", "different")
        assertEquals("Passwords do not match", result)
    }
    
    @Test
    fun `validateName with valid name returns null`() {
        val result = ValidationUtils.validateName("John Doe")
        assertNull(result)
    }
    
    @Test
    fun `validateName with empty name returns error`() {
        val result = ValidationUtils.validateName("")
        assertEquals("Name cannot be empty", result)
    }
    
    @Test
    fun `validatePhoneNumber with valid phone returns null`() {
        val result = ValidationUtils.validatePhoneNumber("1234567890")
        assertNull(result)
    }
    
    @Test
    fun `validatePhoneNumber with empty phone returns error`() {
        val result = ValidationUtils.validatePhoneNumber("")
        assertEquals("Phone number cannot be empty", result)
    }
}

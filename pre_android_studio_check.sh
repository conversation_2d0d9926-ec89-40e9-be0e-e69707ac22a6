#!/bin/bash

echo "🔍 Pre-Android Studio Launch Verification"
echo "========================================"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo ""
echo "📱 Checking if project is ready for Android Studio..."
echo ""

# Check critical files
critical_files=(
    "build.gradle.kts"
    "settings.gradle.kts" 
    "gradle.properties"
    "app/build.gradle.kts"
    "app/src/main/AndroidManifest.xml"
    "app/src/main/java/com/saiharvest/orderapp/MainActivity.kt"
    "app/src/main/java/com/saiharvest/orderapp/SaiHarvestApplication.kt"
)

echo "🔧 Critical Files Check:"
all_files_present=true
for file in "${critical_files[@]}"; do
    if [ -f "$file" ]; then
        echo -e "  ✅ $file"
    else
        echo -e "  ❌ Missing: $file"
        all_files_present=false
    fi
done

echo ""
echo "📊 Project Statistics:"
echo "====================="

# Count files
kotlin_files=$(find app/src/main/java -name "*.kt" 2>/dev/null | wc -l)
resource_files=$(find app/src/main/res -name "*.xml" 2>/dev/null | wc -l)
test_files=$(find app/src/test -name "*.kt" 2>/dev/null | wc -l)

echo "📝 Kotlin source files: $kotlin_files"
echo "📱 Resource files: $resource_files"
echo "🧪 Test files: $test_files"

# Check package structure
echo ""
echo "📦 Package Structure:"
echo "===================="
if [ -d "app/src/main/java/com/saiharvest/orderapp" ]; then
    echo -e "✅ Main package: com.saiharvest.orderapp"
    
    # Check layers
    if [ -d "app/src/main/java/com/saiharvest/orderapp/domain" ]; then
        echo -e "✅ Domain layer present"
    fi
    if [ -d "app/src/main/java/com/saiharvest/orderapp/data" ]; then
        echo -e "✅ Data layer present"
    fi
    if [ -d "app/src/main/java/com/saiharvest/orderapp/presentation" ]; then
        echo -e "✅ Presentation layer present"
    fi
    if [ -d "app/src/main/java/com/saiharvest/orderapp/di" ]; then
        echo -e "✅ Dependency injection present"
    fi
else
    echo -e "❌ Main package structure missing"
    all_files_present=false
fi

echo ""
echo "🎯 Android Studio Launch Instructions:"
echo "====================================="
echo "1. Open Android Studio"
echo "2. Choose 'Open an Existing Project'"
echo "3. Navigate to: $(pwd)"
echo "4. Select this folder and click 'Open'"
echo "5. Wait for Gradle sync to complete"
echo "6. Click the green 'Run' button"

echo ""
if $all_files_present; then
    echo -e "${GREEN}🎉 Project is ready for Android Studio!${NC}"
    echo -e "${GREEN}All critical files are present and properly structured.${NC}"
else
    echo -e "${RED}⚠️  Some files are missing. Please check the issues above.${NC}"
fi

echo ""
echo -e "${BLUE}📱 Expected behavior when you run the app:${NC}"
echo "1. App launches with 'Sai Harvest Foods' login screen"
echo "2. You can register a new user"
echo "3. You can login with registered credentials"
echo "4. Navigation works between screens"
echo "5. Light/dark theme switching works"

echo ""
echo -e "${YELLOW}💡 If you encounter issues in Android Studio:${NC}"
echo "1. Share the error message with me"
echo "2. I'll help you troubleshoot and fix it"
echo "3. Common issues are usually easy to resolve"

echo ""
echo "🚀 Ready to launch in Android Studio!"
